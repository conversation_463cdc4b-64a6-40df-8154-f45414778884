import { useState } from 'react';
import { useParams } from 'react-router-dom';
import DInput from '@/components/Global/DInput/DInput';
import DButton from '@/components/Global/DButton';
import DContentUpload from '@/components/Global/DContentUpload';
import { v4 as uuidv4 } from 'uuid';

const ImageForm = ({ setIsSaving, closeModal, item, slots, setSlots, chatbotId }) => {
  const params = useParams();
  const [imageUrl, setImageUrl] = useState(item?.url ?? 'https://');
  const [imageFile, setImageFile] = useState(null);
  const [imageTitle, setImageTitle] = useState(item?.title ?? '');
  const [imageDescription, setImageDescription] = useState(item?.description ?? '');
  const [error, setError] = useState({});
  const [submitted, setSubmitted] = useState(false);

  const validateForm = () => {
    let errors = {};

    // Validate image - either URL or file is required
    if (!imageFile && imageUrl === '') {
      errors.imageUrl = 'Image URL or file is required';
    }
    if (imageTitle === '') {
      errors.imageTitle = 'Image title is required';
    }

    setError(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = async () => {
    setSubmitted(true);
    if (!validateForm()) return;

    setIsSaving(true);
    const payload = {
      kb_id: params.id ?? chatbotId,
      url: imageFile ? URL.createObjectURL(imageFile) : imageUrl,
      image_file: imageFile,
      title: imageTitle,
      description: imageDescription,
      order: slots.length + 1,
      frontend_id: uuidv4(),
    }

    setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'image', disclaimer: 'Image' }]);
    closeModal();
  };

  const handleUpdate = async () => {
    setSubmitted(true);
    if (!validateForm()) return; 

    setIsSaving(true);

    setSlots((prevSlots) => 
      prevSlots.map(slot => 
        (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? { 
          ...slot, 
          url: imageFile ? URL.createObjectURL(imageFile) : imageUrl,
          image_file: imageFile,
          title: imageTitle, 
          description: imageDescription, 
          order: item.order,
          disclaimer: 'Image',
          frontend_id: item.frontend_id,
        } : slot
      )
    );

    closeModal();
    setIsSaving(false);
  };

  return (
    <div className="flex flex-col gap-size5">
      <DContentUpload
        label="Image"
        urlPlaceholder="Enter image URL"
        urlValue={imageUrl}
        onUrlChange={(e) => setImageUrl(e.target.value)}
        onFileChange={(file) => {
          setImageFile(file);
          setImageUrl(''); // Clear URL when file is selected
        }}
        acceptedFileTypes="image/*"
        maxFileSize="10MB"
        supportedFormats="Images: PNG, JPEG, JPG, GIF, SVG"
        error={submitted ? error['imageUrl'] : ''}
      />
      
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Image title</p>
        <DInput 
          placeholder="Enter image title" 
          value={imageTitle} 
          onChange={(e) => setImageTitle(e.target.value)} 
          error={submitted ? error['imageTitle'] : ''}
        />
      </div>
      
      <div className="flex flex-col gap-size1">
        <p className="text-base font-medium tracking-tight">Image description</p>
        <DInput 
          placeholder="Enter image description" 
          minRows={3} 
          value={imageDescription} 
          onChange={(e) => setImageDescription(e.target.value)} 
          error={submitted ? error['imageDescription'] : ''}
        />
      </div>

      {item && Object.keys(item).length > 0 ? (
        <DButton variant="dark" onClick={handleUpdate} fullWidth>Update</DButton>
      ) : (
        <DButton variant="dark" onClick={handleSubmit} fullWidth>Complete</DButton>
      )}
    </div>
  );
};

export default ImageForm;
