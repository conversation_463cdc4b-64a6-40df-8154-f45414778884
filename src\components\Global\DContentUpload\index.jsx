import { useState } from 'react';
import DInput from '../DInput/DInput';
import DUpload from '../DUpload';
import LinkIcon from '../Icons/LinkIcon';
import UploadIcon from '../Icons/UploadIcon';

const DContentUpload = ({
  label,
  urlPlaceholder = "Enter URL",
  urlValue,
  onUrlChange,
  onFileChange,
  acceptedFileTypes = "image/*,video/*",
  maxFileSize = "50MB",
  supportedFormats = "Images: PNG, JPEG, JPG, GIF, SVG • Videos: MP4, MOV, AVI, WebM",
  urlIcon = <LinkIcon />,
  error,
  className = "",
  showTabs = true,
  defaultTab = "url", // "url" or "file"
  ...props
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const handleFileUpload = (e) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      onFileChange?.(files[0]);
    }
  };

  if (!showTabs) {
    // Simple URL input without tabs
    return (
      <div className={`flex flex-col gap-size1 ${className}`}>
        {label && (
          <p className="text-base font-medium tracking-tight">{label}</p>
        )}
        <DInput
          placeholder={urlPlaceholder}
          value={urlValue}
          onChange={onUrlChange}
          icon={urlIcon}
          error={error}
          {...props}
        />
      </div>
    );
  }

  return (
    <div className={`flex flex-col gap-size2 ${className}`}>
      {label && (
        <p className="text-base font-medium tracking-tight">{label}</p>
      )}
      
      {/* Tab Navigation */}
      <div className="flex gap-size1">
        <button
          className={`dbutton flex w-full items-center justify-center rounded-size2 h-9 gap-size0 ${
            activeTab === 'url' ? 'bg-purple-100' : 'bg-grey-1'
          }`}
          onClick={() => setActiveTab('url')}
        >
          <LinkIcon
            className={`${
              activeTab === 'url' ? 'text-purple-300' : 'text-black'
            }`}
          />
          <span
            className={`${
              activeTab === 'url' ? 'text-purple-300' : 'text-black'
            } text-xs`}
          >
            Add URL
          </span>
        </button>
        <button
          className={`dbutton flex w-full items-center justify-center rounded-size2 h-9 gap-size0 ${
            activeTab === 'file' ? 'bg-purple-100' : 'bg-grey-1'
          }`}
          onClick={() => setActiveTab('file')}
        >
          <UploadIcon
            className={`${
              activeTab === 'file' ? 'text-purple-300' : 'text-black'
            }`}
          />
          <span
            className={`${
              activeTab === 'file' ? 'text-purple-300' : 'text-black'
            } text-xs`}
          >
            Upload File
          </span>
        </button>
      </div>

      {/* Tab Content */}
      <div className="min-h-[120px]">
        {activeTab === 'url' && (
          <div className="flex flex-col gap-size1">
            <DInput
              placeholder={urlPlaceholder}
              value={urlValue}
              onChange={onUrlChange}
              icon={urlIcon}
              error={error}
              {...props}
            />
          </div>
        )}
        
        {activeTab === 'file' && (
          <div className="flex flex-col gap-size1 ">
            <DUpload
              onChangeFile={handleFileUpload}
              accept={acceptedFileTypes}
              note={`Max. size ${maxFileSize}`}
            />
            <p className="text-grey-20 text-xs font-light">
              {supportedFormats}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default DContentUpload;
