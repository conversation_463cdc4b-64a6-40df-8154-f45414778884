import DButton from '@/components/Global/DButton';
import DTable from '@/components/Global/DTable';
import AddIcon from '@/components/Global/Icons/AddIcon';
import useDanteApi from '@/hooks/useDanteApi';
import LayoutMain from '@/layouts/LayoutMain';
import * as agentService from '@/services/agent.service';
import * as humanHandoverService from '@/services/human-handover-organization.js';
import { useEffect, useState } from 'react';
import AddMember from './AddAgent';
import useToast from '@/hooks/useToast';
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/react';
import OptionsHorizontalIcon from '@/components/Global/Icons/OptionsHorizontalIcon';
import EditIcon from '@/components/Global/Icons/EditIcon';
import DeleteIcon from '@/components/Global/Icons/DeleteIcon';
import ResetIcon from '@/components/Global/Icons/ResetIcon';
import { useUserStore } from '@/stores/user/userStore';
import {
  getDynamicImage,
  getDynamicTeamImage,
} from '@/helpers/getDynamicImage';
import DConfirmationModal from '@/components/Global/DConfirmationModal';
import DModal from '@/components/Global/DModal';
import EditMember from './EditAgent';
import DLoading from '@/components/DLoading';
import DSelect from '@/components/Global/DSelect';
import { useHumanHandoverStore } from '@/stores/humanHandover/humanHandoverStore';
import AddOrganization from './AddOrganization';
import * as userService from '@/services/user.service';
import DInput from '@/components/Global/DInput/DInput';
import DButtonIcon from '@/components/Global/DButtonIcon';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import CloseIcon from '@/components/Global/Icons/CloseIcon';
import { AGENT_MEMBER_PERMISSIONS } from '@/constants';
import { useNavigate } from 'react-router-dom';
import DTitle from '@/components/Global/DTitle';
import featureCheck from '@/helpers/tier/featureCheck';

const HumanHandover = () => {
  const navigate = useNavigate();
  const {
    isLoading: isLoadingOrganizations,
    data: dataOrganizations,
    refetch: refetchOrganizations,
  } = useDanteApi(humanHandoverService.getOrganizations);

  const { selectedOrganizationId, setSelectedOrganizationId, userOrganization, setUserOrganization } =
    useHumanHandoverStore();

  const {
    isLoading: isLoadingOrganizationInfo,
    data: dataOrganizationInfo,
    refetch: refetchOrganizationInfo,
  } = useDanteApi(
    humanHandoverService.getOrganizationById,
    [selectedOrganizationId],
    {
      skip: !selectedOrganizationId, // Only fetch when selectedOrganizationId is available
    },
    selectedOrganizationId
  );

  const { addSuccessToast } = useToast();
  const [tableData, setTableData] = useState([]);
  const [organizations, setOrganizations] = useState([]);
  const [hasOrganizationOwner, setHasOrganizationOwner] = useState(false);
  const [openAddMember, setOpenAddMember] = useState(false);

  const userData = useUserStore((state) => state.user);
  const [newMember, setNewMember] = useState({
    name: '',
    email: '',
    agent_title: '',
    chatbots: [],
    all_knowledge_bases: false,
  });
  const [isLoadingMember, setIsLoadingMember] = useState(false)

  const [openAddOrganization, setOpenAddOrganization] = useState(false);
  const [newOrganization, setNewOrganization] = useState({
    name: '',
    icon: '',
  });


  const [iconOrganizationPreview, seticonOrganizationPreview] = useState(null);
  const [iconOrganizationFile, seticonOrganizationFile] = useState(null);

  const [openDeleteMember, setOpenDeleteMember] = useState(false);
  const [deleteMember, setDeleteMember] = useState(null);
  const [selectedChatbots, setSelectedChatbots] = useState([]);

  const [openCancelInvitation, setOpenCancelInvitation] = useState(false);
  const [cancelInvitationMember, setCancelInvitationMember] = useState(null);
  //edit member
  const [openEditMember, setOpenEditMember] = useState(false);
  const [editMember, setEditMember] = useState(null);

  const [editOrganizationName, setEditOrganizationName] = useState(false);
  const [organizationName, setOrganizationName] = useState('');

  // Owner role editing states
  const [editingOwnerRole, setEditingOwnerRole] = useState(false);
  const [ownerRole, setOwnerRole] = useState('');

  const tableColumns = [
    {
      label: 'Human Handover Name',
      key: 'member',
      showName: true,
    },
    {
      label: 'Email',
      key: 'email',
      showName: true,
    },
    {
      label: 'Permissions',
      key: 'permissions',
      showName: true,
    },
    {
      label: 'Confirmed',
      key: 'accepted',
      showName: true,
    },
    {
      label: 'Actions',
      key: 'actions',
      showName: false,
      minWidth: 'min-w-10',
    },
  ];

  const createMember = async () => {
    setIsLoadingMember(true)
    try {
      const response = await humanHandoverService.addOrganizationMember(
        dataOrganizationInfo.organization_id,
        newMember
      );

      if (response.status === 200) {
        addSuccessToast({
          title: 'Success',
          message: 'Agent created successfully',
        });
        setOpenAddMember(false);
        setNewMember({
          name: '',
          email: '',
          agent_title: '',
          chatbots: [],
        });
        setSelectedChatbots([]);
        refetchOrganizations();
        refetchOrganizationInfo();
      }
    } catch (error) {
      console.log('Error creating agent', error);
    }finally{
      setIsLoadingMember(false)
    }
  };

  const deleteTeamMember = async () => {
    try {
      const response = await humanHandoverService.deleteOrganizationMember(
        dataOrganizationInfo.organization_id,
        deleteMember
      );
      if (response.status === 200) {
        addSuccessToast({
          title: 'Success',
          message: 'Member removed successfully',
        });
        setOpenDeleteMember(false);
        refetchOrganizationInfo();
      }
    } catch (error) {
      console.log('Error removing agent', error);
    }
  };

  const handleCancelInvitation = async () => {
    try {
      const response = await humanHandoverService.cancelInvitation(
        dataOrganizationInfo.organization_id,
        cancelInvitationMember
      );
      if (response.status === 200) {
        addSuccessToast({
          title: 'Success',
          message: 'Invitation canceled successfully',
        });
        setOpenCancelInvitation(false);
        refetchOrganizationInfo();
      }
    } catch (error) {
      console.log('Error canceling invitation', error);
    }
  };

  const handleEditTeamMember = async () => {
    try {
      await humanHandoverService.updateOrganizationMember(
        dataOrganizationInfo.organization_id,
        editMember.user_response.id,
        editMember
      );

      // Update the store if the current user is editing their own role
      if (userOrganization && editMember.user_response.id === userData.id) {
        setUserOrganization({
          ...userOrganization,
          agent_title: editMember.agent_title
        });
      }

      addSuccessToast({
        title: 'Success',
        message: 'Member updated successfully',
      });
      refetchOrganizationInfo();
      setOpenEditMember(false);
    } catch (error) {
      console.log('Error editing member', error);
    }
  };

  const handleSaveOwnerRole = async () => {
    try {
      await humanHandoverService.updateOrganizationMember(
        dataOrganizationInfo?.organization_id,
        userData.id,
        { agent_title: ownerRole }
      );

      // Update the store with the new agent title
      if (userOrganization) {
        setUserOrganization({
          ...userOrganization,
          agent_title: ownerRole
        });
      }

      addSuccessToast({
        title: 'Success',
        message: 'Role updated successfully',
      });
      refetchOrganizationInfo();
      setEditingOwnerRole(false);
    } catch (error) {
      console.log('Error updating role', error);
    }
  };

  const setTeamData = (data, organizationInfo) => {
    if (data) {
      const teamData = data?.map((member) => {
        return {
          member: (
            <p
              className={
                organizationInfo?.owner_id === userData.id && !member.accepted
                  ? 'text-grey-50'
                  : 'text-black'
              }
            >
              {member.name}{' '}
              <span className="text-grey-20">
                {member.user_response.id === userData.id && '(You)'}
                {organizationInfo?.owner_id === userData.id && !member.accepted && '(Pending)'}
              </span>
            </p>
          ),
          email: member.user_response.email,
          permissions: AGENT_MEMBER_PERMISSIONS.find(
            (permission) => permission.value === member.permissions
          )?.name,
          icon:
            member.image ?? getDynamicImage(Math.floor(Math.random() * 6) + 1),
          accepted: member.accepted ? 'Yes' : 'No',
          last_login: new Date(member.last_login).toLocaleDateString('en-US', {
            month: 'long',
            day: 'numeric',
            year: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
          }),
          actions: organizationInfo?.owner_id === userData.id && (
            <Menu>
              <MenuButton className="flex items-center justify-end gap-size2 w-full">
                <div
                  size="lg"
                  className="hover:bg-grey-5 rounded-size0 size-10 flex items-center justify-center"
                >
                  <OptionsHorizontalIcon />
                </div>
              </MenuButton>
              <MenuItems
                transition
                anchor="bottom end"
                className="border border-grey-5 rounded-size0 p-size0 flex flex-col gap-size1 bg-white"
              >
                {organizationInfo?.owner_id === member.user_response.id ? (
                  // Owner can only edit their own role
                  <MenuItem
                    onClick={() => {
                      setOwnerRole(member.agent_title || '');
                      setEditingOwnerRole(true);
                    }}
                  >
                    <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                      <EditIcon />
                      <span className="text-xs font-regular tracking-tight">
                        Edit role
                      </span>
                    </div>
                  </MenuItem>
                ) : (
                  // Owner can edit other members
                  <MenuItem
                    onClick={() => {
                      setEditMember(member);
                      setOpenEditMember(true);
                    }}
                  >
                    <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                      <EditIcon />
                      <span className="text-xs font-regular tracking-tight">
                        Edit agent
                      </span>
                    </div>
                  </MenuItem>
                )}
                {/* Only show additional options for non-owner members */}
                {organizationInfo?.owner_id !== member.user_response.id && (
                  <>
                    {member.accepted && (
                      <MenuItem
                        onClick={() => {
                          setOpenDeleteMember(true);
                          setDeleteMember(member.user_response.id);
                        }}
                      >
                        <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                          <DeleteIcon />
                          <span className="text-xs font-regular tracking-tight">
                            Remove member
                          </span>
                        </div>
                      </MenuItem>
                    )}
                    {!member.accepted &&
                      member.user_response.email !== userData.email && (
                        <MenuItem>
                          <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                            <ResetIcon />
                            <span className="text-xs font-regular tracking-tight">
                              Resend Invitation
                            </span>
                          </div>
                        </MenuItem>
                      )}
                    {!member.accepted &&
                      member.user_response.email !== userData.email && (
                        <MenuItem
                          onClick={() => {
                            setOpenCancelInvitation(true);
                            setCancelInvitationMember(member.user_response.id);
                          }}
                        >
                          <div className="p-size0 rounded-size0 hover:bg-grey-5 flex items-center gap-size1">
                            <CloseIcon />
                            <span className="text-xs font-regular tracking-tight">
                              Cancel Invitation
                            </span>
                          </div>
                        </MenuItem>
                      )}
                  </>
                )}
              </MenuItems>
            </Menu>
          ),
        };
      });
      setTableData(teamData);
    } else {
      setTableData([]);
    }
  };

  const createOrganization = async () => {
    try {
      const formData = new FormData();

      if (iconOrganizationFile instanceof File) {
        formData.append('file', iconOrganizationFile);
        const response = await userService.uploadFile(formData);
        if (response.status === 200) {
          newOrganization.icon = response?.data?.url;
        }
        formData.delete('file');
      }

      const response = await humanHandoverService.createOrganization(
        newOrganization
      );

      if (response.status === 200) {
        addSuccessToast({
          title: 'Success',
          message: 'Organization created successfully',
        });
        setSelectedOrganizationId(response.data.organization_id);
        refetchOrganizations();
        setOpenAddOrganization(false);
      }
    } catch (error) {
      console.log('Error creating organization', error);
    }
  };

  const handleImageChange = (e, field) => {
    const handleIconUpdate = (previewURL, file) => {
      seticonOrganizationPreview(previewURL);
      seticonOrganizationFile(file);
    };

    if (!e) {
      setNewOrganization((prev) => ({ ...prev, [field]: '' }));
      handleIconUpdate(null, null);
      return;
    }

    const file = e;
    const previewURL = file instanceof File ? URL.createObjectURL(file) : file;

    setNewOrganization((prev) => ({ ...prev, [field]: previewURL }));

    if (field === 'icon') {
      handleIconUpdate(previewURL, file instanceof File ? file : null);
    }
  };

  const handleEditOrganizationName = async () => {
    const response = await humanHandoverService.updateOrganization(
      dataOrganizationInfo.organization_id,
      {
        name: organizationName,
      }
    );
    if (response.status === 200) {
      addSuccessToast({
        title: 'Success',
        message: 'Organization updated successfully',
      });
      refetchOrganizations();
      refetchOrganizationInfo();
    }
  };



  useEffect(() => {
    if (dataOrganizations?.results?.length > 0) {
      const organizationOwner = dataOrganizations?.results?.find(
        (organization) => organization.owner_id === userData?.id
      );
      setHasOrganizationOwner(!!organizationOwner);
      setOrganizations(dataOrganizations?.results);

      if (selectedOrganizationId === null) {
        setSelectedOrganizationId(organizationOwner?.organization_id || dataOrganizations?.results[0]?.organization_id);
      }
    }
  }, [dataOrganizations]);

  // Update table data when organization info changes
  useEffect(() => {
    if (dataOrganizationInfo?.members) {
      setTeamData(dataOrganizationInfo.members, dataOrganizationInfo);
    } else {
      setTableData([]);
    }
  }, [dataOrganizationInfo]);

  // Auto-refresh organization data every 30 seconds to catch status updates (silent background refresh)
  useEffect(() => {
    if (!selectedOrganizationId) return;

    const interval = setInterval(() => {
      // Silent refetch - this won't trigger loading states
      refetchOrganizationInfo();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [selectedOrganizationId, refetchOrganizationInfo]);

  // Refresh data when user returns to the tab (silent background refresh)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && selectedOrganizationId) {
        // Silent refetch when user returns to tab
        refetchOrganizationInfo();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [selectedOrganizationId, refetchOrganizationInfo]);

  // Only show loading for initial organization load, not for background refetches
  if (isLoadingOrganizations || (selectedOrganizationId && isLoadingOrganizationInfo && !dataOrganizationInfo)) {
    return <DLoading show={true} />;
  }

  return (
    <LayoutMain title="">
      <header className="flex justify-between items-center md:items-start 3xl:max-w-[1200px] 3xl:mx-auto">
        <DTitle title="Human Handover" className="text-lg md:text-xl" />
        <div className="max-w-40 md:max-w-80 w-full">
          {organizations.length > 0 && <DSelect
            options={
              organizations?.map((organization) => ({
                label: organization.name,
                value: organization.organization_id,
              })) || []
            }
            onChange={(organization_id) => {
              setSelectedOrganizationId(organization_id);
            }}
            value={selectedOrganizationId}
            placeholder="Select an organization"
          />}
        </div>
      </header>
      <div className="3xl:max-w-[1200px] 3xl:mx-auto h-full mt-size3 md:mt-0">
        {organizations.length > 0 && (
          <div className="flex flex-col gap-size2">
            <div className="flex flex-col gap-size2 md:gap-0 md:flex-row items-center h-auto md:h-11 mb-size2">
              <div className="flex w-full items-center gap-size3">
                {dataOrganizationInfo && (
                  <img
                    src={
                      dataOrganizationInfo?.icon ||
                      getDynamicTeamImage(Math.floor(Math.random() * 6) + 1)
                    }
                    alt="Organization icon"
                    className="w-10 h-10 rounded-size0"
                  />
                )}
                {editOrganizationName ? (
                  <DInput
                    value={organizationName || dataOrganizationInfo?.name}
                    onChange={(e) => {
                      setOrganizationName(e.target.value);
                    }}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        setEditOrganizationName(false);
                        handleEditOrganizationName();
                      }
                    }}
                    className="rounded-none border-t-0 border-r-0 border-l-0"
                  />
                ) : (
                  <p className="text-xl font-regular tracking-tight">
                    {organizationName || dataOrganizationInfo?.name}
                  </p>
                )}
                <div className="flex items-center gap-size2">
                  <DButtonIcon
                    size="lg"
                    className={`border border-grey-5 rounded-size0 size-8 ${
                      dataOrganizationInfo?.owner_id !== userData?.id
                        ? 'hidden'
                        : ''
                    }`}
                    onClick={() => {
                      if (editOrganizationName) {
                        setEditOrganizationName(false);
                        handleEditOrganizationName();
                      } else {
                        setEditOrganizationName(true);
                      }
                    }}
                  >
                    {editOrganizationName ? <CheckmarkIcon /> : <EditIcon />}
                  </DButtonIcon>
                  {editOrganizationName && (
                    <DButtonIcon
                      size="lg"
                      className="border border-grey-5 rounded-size0 size-8"
                      onClick={() => setEditOrganizationName(false)}
                    >
                      <CloseIcon />
                    </DButtonIcon>
                  )}
                </div>
              </div>

              {!hasOrganizationOwner && selectedOrganizationId && userData?.tier_type !== 'free' && (
                <DButton
                  variant="dark"
                  className=""
                  onClick={() => {
                    setOpenAddOrganization(true);
                  }}
                >
                  <AddIcon />
                  Add Organization
                </DButton>
              )}
            </div>

            {selectedOrganizationId && <DTable columns={tableColumns} data={tableData} />}

            {tableData?.length === 0 && selectedOrganizationId && (
              <div className="flex justify-center items-center w-full h-full">
                <div className="flex flex-col gap-4 w-full max-w-72 items-center">
                  <p className="text-sm text-gray-500 text-center">
                    Approved Human Handover will appear here.
                  </p>
                  {/* <DButton
                    variant="grey"
                    className=""
                    onClick={() => {
                      setOpenAddMember(true);
                    }}
                  >
                    <AddIcon />
                    Add Human Handover
                  </DButton> */}
                </div>
              </div>
            )}
            {dataOrganizations?.results?.length > 0 && (
              <div className="flex flex-col md:flex-row md:items-center gap-size2 md:justify-between">
                <DButton
                  variant="grey"
                  className="w-max"
                  onClick={() => {
                    window.open(
                      `/human-handover/${dataOrganizationInfo?.organization_id}`,
                      '_blank'
                    );
                  }}
                >
                  Open Human Handover Dashboard
                </DButton>
                {dataOrganizationInfo?.owner_id === userData?.id && (
                  <DButton
                    variant="grey"
                    className=""
                    onClick={() => {
                      setOpenAddMember(true);
                    }}
                  >
                    <AddIcon />
                    Add Agent
                  </DButton>
                )}
              </div>
            )}
            <AddMember
              open={openAddMember}
              onClose={() => {
                setOpenAddMember(false);
                setNewMember({
                  name: '',
                  email: '',
                  agent_title: '',
                  chatbots: [],
                });
                setSelectedChatbots([]);
              }}
              onSubmit={createMember}
              newMember={newMember}
              setNewMember={setNewMember}
              selectedChatbots={selectedChatbots}
              setSelectedChatbots={setSelectedChatbots}
              isLoadingMember={isLoadingMember}
            />

            <EditMember
              open={openEditMember}
              onClose={() => {
                setOpenEditMember(false);
                setEditMember(null);
              }}
              editAgent={editMember}
              setMember={setEditMember}
              onSave={handleEditTeamMember}
              deleteTeamMember={() => setOpenDeleteMember(true)}
              setDeleteMember={setDeleteMember}
            />
          </div>
        )}
        {(organizations.length === 0 || !organizations.some(org => org?.owner_id === userData.id)) && !selectedOrganizationId && (
          <div className="flex flex-1 h-[calc(100%-5rem)] flex-col gap-[48px] items-center justify-center">
            <div className="flex flex-col gap-size3 items-center w-full md:max-w-[400px] justify-center">
              <p className="text-xl font-regular tracking-tight text-center">
                Ready to create your Human Handover organization?
              </p>

              <DButton
                variant="dark"
                onClick={() => {
                  if(featureCheck('live_agent')){
                    setOpenAddOrganization(true);
                  }
                }}
              >
                <AddIcon />
                Create Organization
              </DButton>
            </div>
          </div>
        )}
      </div>
      <AddOrganization
        open={openAddOrganization}
        onClose={() => setOpenAddOrganization(false)}
        onSubmit={createOrganization}
        newOrganization={newOrganization}
        setNewOrganization={setNewOrganization}
        handleImageChange={handleImageChange}
        iconOrganizationPreview={iconOrganizationPreview}
        iconOrganizationFile={iconOrganizationFile}
      />
      <DConfirmationModal
        open={openDeleteMember}
        onClose={() => setOpenDeleteMember(false)}
        onConfirm={deleteTeamMember}
        title="Remove member"
        description="Are you sure you want to remove this member?"
        confirmText="Remove"
        cancelText="Cancel"
        variantConfirm="danger"
      />
      <DConfirmationModal
        open={openCancelInvitation}
        onClose={() => setOpenCancelInvitation(false)}
        onConfirm={handleCancelInvitation}
        title="Cancel invitation"
        description="Are you sure you want to cancel this invitation?"
        confirmText="Cancel"
        cancelText="Cancel"
        variantConfirm="danger"
      />

      {/* Owner Role Edit Modal */}
      <DModal
        title="Edit Role"
        isOpen={editingOwnerRole}
        onClose={() => setEditingOwnerRole(false)}
        footer={
          <div className="flex items-center gap-size1 w-full">
            <DButton
              onClick={() => setEditingOwnerRole(false)}
              variant="grey"
              fullWidth
              size="md"
            >
              Cancel
            </DButton>
            <DButton
              onClick={handleSaveOwnerRole}
              variant="dark"
              fullWidth
              size="md"
            >
              Save
            </DButton>
          </div>
        }
      >
        <div className="flex flex-col gap-size3">
          <div className="flex flex-col gap-size0">
            <p className="text-base font-medium tracking-tight">Role</p>
            <DInput
              placeholder="Enter your role"
              value={ownerRole}
              onChange={(e) => setOwnerRole(e.target.value)}
            />
          </div>
        </div>
      </DModal>
    </LayoutMain>
  );
};

export default HumanHandover;
