import React, { useState, useEffect } from 'react';
import DModal from '@/components/Global/DModal';
import DButton from '@/components/Global/DButton';
import CheckmarkIcon from '@/components/Global/Icons/CheckmarkIcon';
import InfoIcon from '@/components/Global/Icons/InfoIcon';
import useDanteApi from '@/hooks/useDanteApi';
import * as plansService from '@/services/plans.service';
import { useUserStore } from '@/stores/user/userStore';
import DModalDowngrade from '../DModalDowngrade';
import DTooltip from '@/components/Global/DTooltip';
import { Transition } from '@headlessui/react';
import clsx from 'clsx';
import { trackAddonSubscription } from '@/helpers/analytics';
import NewBadge from '@/components/Global/NewBadge';

// Define the features list by plan
const FREE_FEATURES = [
  { label: '1 AI Chatbot' },
  { label: '1,000 messages/month', hasTooltip: true },
  { label: '100+ Languages' },
  { label: 'Basic LLMs' },
  { label: 'Email Support' },
  { label: 'Custom Styling' },
  { label: 'Custom Personalities' },
  { label: 'AI Chatbot Deployment' },
];

const STARTER_FEATURES = [
  { label: 'Build 5 AI Voice Agents', underlineTooltip:true, content: 'Take your AI to the next level — build an AI voice agent. This is more than a chatbot. It\'s a fully interactive voice assistant powered by real-time AI, designed to engage, persuade, and convert — all through natural conversation and available via browser or direct phone call.', showNewBadge: true },
  { label: 'Access to fast LLM models' },
  { label: '12,000 messages/month', hasTooltip: true },
  { label: '5 AI chatbots' },
  { label: '0 team member seats' },
  { label: 'Realtime AI voice access' },
  { label: 'Custom styling and personalities' },
  { label: 'Embed unlimited websites' },
  { label: 'Unlimited links to train on' },
  { label: 'Unlimited files to train on' },
  { label: 'Chat records' },
  { label: 'Enhanced chatbot security' },
  { label: '100+ languages' },
  { label: 'Zapier integration', underlineTooltip: true, content: 'Integrate with over 7000 applications via Zapier' },
  { label: 'Advanced analytics'},
  { label: 'Priority email support' },
];

const ADVANCED_FEATURES = [
  { label: 'Build 10 AI Voice Agents', underlineTooltip:true, content: 'Take your AI to the next level — build an AI voice agent. This is more than a chatbot. It\'s a fully interactive voice assistant powered by real-time AI, designed to engage, persuade, and convert — all through natural conversation and available via browser or direct phone call.', showNewBadge: true },
  { label: 'Access to advanced LLM models' },
  { label: '50,000 messages/month', hasTooltip: true },
  { label: '10 AI chatbots' },
  { label: 'Everything in Starter, plus:' },
  { label: 'Remove "Powered by Dante AI"' },
  { label: 'Human handover' },
  { label: 'Team management' },
  { label: '3 team member seats' },
  { label: 'AI lead generation' },
  { label: 'Advanced integrations', underlineTooltip: true, content: 'Whatsapp and Intercom integration' },
  { label: 'Calendly booking agent' },
  { label: 'Custom CSS editor' },
  { label: 'Conversation rate limiting' },
  // { label: "Memory auto-refresh" },
  { label: 'Unanswered message recognition' },
  { label: 'One-on-one video call support' },

];

const PRO_FEATURES = [
  { label: 'Build 50 AI Voice Agents', underlineTooltip:true, content: 'Take your AI to the next level — build an AI voice agent. This is more than a chatbot. It\'s a fully interactive voice assistant powered by real-time AI, designed to engage, persuade, and convert — all through natural conversation and available via browser or direct phone call.', showNewBadge: true },
  { label: 'Access to all LLM models' },
  { label: '200,000 messages/month', hasTooltip: true },
  { label: '50 AI chatbots' },
  { label: 'Everything in Advanced, plus:' },
  { label: 'Personalized onboarding consultation' },
  { label: 'Remove "Powered by Dante AI"' },
  { label: '10 team member seats' },
  { label: 'API access' },
  { label: 'API setup support' },
  { label: 'Uptime SLA guarantee' },
  { label: 'Custom integrations', underlineTooltip: true, content: 'Integrate your custom needs for your brand' },
  { label: 'Full white-label experience' },
  { label: 'Built with resellers in mind' },
  { label: 'Dedicated account manager' },
];

// Tooltip content for messages feature
const MESSAGES_TOOLTIP =
  'This is the maximum number of messages your AI chatbot will send per month, depending on which LLM you use. For a full breakdown of LLM message costs, visit the FAQ here.';

// Plan descriptions
const PLAN_DESCRIPTIONS = {
  Starter: 'For small businesses',
  Advanced: 'For businesses wanting more robust AI features',
  Pro: 'For businesses wanting more advanced AI',
};

// Billing periods
const BILLING_PERIODS = [
  { id: 'weekly', label: 'Weekly' },
  { id: 'monthly', label: 'Monthly' },
  { id: 'yearly', label: 'Annual', badge: '2 months free' }
];

// Addons for weekly plan
const WEEKLY_ADDONS = [
  { id: 'remove_powered_by_dante_ai', name: 'Remove Powered by Dante AI', price: 29, description: 'White-label your AI chatbot', addon_id: '' },
  { id: 'extra_chatbot', name: 'Add Chatbot', price: 5, description: 'Add one extra AI chatbot', addon_id: '' },
  { id: 'extra_seat', name: 'Add Team Member', price: 9, description: 'Add one additional team member', addon_id: '' },
];

const DModalPlans = ({ isOpen, onClose, title, subtitle}) => {
  const user = useUserStore((state) => state.user);


  const [selectedPeriod, setSelectedPeriod] = useState('weekly');
  const [nextTier, setNextTier] = useState(null);
  const [isDowngradeModalOpen, setIsDowngradeModalOpen] = useState(false);
  const [isContentReady, setIsContentReady] = useState(false);
  const { data: tiers, isLoading } = useDanteApi(plansService.getTiers);
  const { data: plans } = useDanteApi(plansService.getPlans);

  // Control when to show content after data is loaded
  useEffect(() => {
    if (!isLoading && tiers) {
      // Add a small delay to ensure smooth animation
      const timer = setTimeout(() => {
        setIsContentReady(true);
      }, 300);
      return () => clearTimeout(timer);
    } else {
      setIsContentReady(false);
    }
  }, [isLoading, tiers, isOpen]);

  const period = selectedPeriod;

  const getPlanSavings = (yearlyPrice, monthlyPrice, weeklyPrice) => {
    if (period === 'yearly') {
      let priceYearly = yearlyPrice;
      let priceMonthly = monthlyPrice;
      return Math.round(priceMonthly * 12 - priceYearly);
    } else if (period === 'monthly') {
      let priceWeekly = weeklyPrice
      // if (priceWeekly) {
        return Math.round((priceWeekly * 4) - monthlyPrice);
      // }
    }
    return 0;
  };

  const handleUpgrade = async (tierId) => {
    try {
      const response = await plansService.getCheckoutSession(tierId);
      if (response.status === 200) {
        window.open(response.data.checkout_session.url, '_blank');
        onClose();
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleDowngrade = (tier) => {
    setNextTier(tier);
    setIsDowngradeModalOpen(true);
  };

  const handleContactSales = () => {
    window.open(
      'https://calendly.com/jetelira/enterprise-introduction',
      '_blank'
    );
    onClose();
  };

  // Filter tiers based on user's current tier
  const getFilteredTiers = () => {
    const tierType = user?.tier_type || 0;
    const tierPeriod = user?.tier_key?.period || 0;
    if (tierType === 'pro' || tierType === 'professional' || tierType === 'ultra' || tierType === 'business') {
      return tiers?.[period]?.filter(tier => tier.name.toLowerCase() !== 'enterprise' && tier.name.toLowerCase() !== 'free') || [];
    } else if (tierType === 'free') { // Free tier
      return tiers?.[period]?.filter(tier =>
        ['Starter', 'Advanced', 'Pro', 'entry', 'premium', 'ultra', 'professional', 'enterprise', 'business'].includes(tier.name)
      ) || [];
    } else if (tierType === 'starter' || tierType === 'entry') { // Starter tier
      if (tierPeriod === 'weekly') {
        if (period === 'monthly' || period === 'yearly') {
          return tiers?.[period]?.filter(tier =>
            ['Starter', 'Advanced', 'Pro', 'entry', 'premium', 'ultra', 'professional', 'enterprise', 'business'].includes(tier.name)
          ) || [];
        }
        return tiers?.[period]?.filter(tier =>
          ['Advanced', 'Pro', 'premium', 'ultra', 'professional', 'enterprise', 'business'].includes(tier.name)
        ) || [];
      }
      return tiers?.[period]?.filter(tier =>
        ['Advanced', 'Pro', 'premium', 'ultra', 'professional', 'enterprise', 'business'].includes(tier.name)
      ) || [];
    } else if (tierType === 'advanced' || tierType === 'premium') { // Advanced tier
      if (tierPeriod === 'weekly') {
        if (period === 'monthly' || period === 'yearly') {
          return tiers?.[period]?.filter(tier =>
            ['Starter', 'Advanced', 'Pro', 'entry', 'premium', 'ultra', 'professional', 'enterprise', 'business'].includes(tier.name)
          ) || [];
        }
      }
      return tiers?.[period]?.filter(tier =>
        ['Pro', 'ultra', 'professional', 'enterprise', 'business'].includes(tier.name)
      ) || [];
    }
    return [];
  };

  const filteredTiers = getFilteredTiers();


  const handleAddon = async (addonKey) => {
    try {
      // Find the addon plan by key
      const addonPlan = plans?.find(plan => plan.key === addonKey);

      if (!addonPlan) {
        console.error('Addon plan not found for key:', addonKey);
        return;
      }

      const response = await plansService.getCheckoutSessionAddon(addonPlan.id);

      if (response.status === 200) {
        trackAddonSubscription({
          subscription_id: response.data.checkout_session.id,
          user_id: user.id,
          email: user.email,
          addon_name: addonPlan.name,
          addon_price: addonPlan.price,
        });
        window.open(response.data.checkout_session.url, '_blank');
      }
    } catch (e) {
      console.error('Error handling addon:', e);
    }
  }

  // Determine if a tier should show the "BEST VALUE" label
  // Always show for Advanced plan regardless of monthly/yearly
  const isBestValue = (tierName) => tierName === 'Advanced';

  // Show addons only for Starter and Advanced tiers
  const showAddons = user?.tier_type === 'starter' || user?.tier_type === 'advanced';

  // Don't open the modal until we have data
  const shouldOpenModal = Boolean(isOpen && (tiers || !isLoading));

  return (
    <DModal
      isOpen={shouldOpenModal}
      onClose={onClose}
      title={title || 'Upgrade your plan'}
      subtitle={subtitle || 'Access more power, faster responses, and scalable support with Dante AI.'}
      className="!w-[1100px] max-w-none !p-size2 !pt-size1 !rounded-2xl overflow-hidden  bg-oklch-100 !max-h-[90vh] !min-h-[550px] "
      headerClassName="!gap-size1 !justify-center text-center w-full"
      titleClassName="!text-xl text-center w-full"
      subtitleClassName="!text-xs text-center w-full"
      contentBgColor="pt-size3 bg-oklch-100"
      hideCloseButton={false}
      line={false}
    >
      {/* Loading state */}
      {/* <Transition
        show={isLoading}
        enter="transition-opacity duration-300"
        enterFrom="opacity-0"
        enterTo="opacity-100"
        leave="transition-opacity duration-300"
        leaveFrom="opacity-100"
        leaveTo="opacity-0"
      >
        <div className="flex items-center justify-center h-[50vh] w-full">
          <DLoading show={true} />
        </div>
      </Transition> */}

      {/* Content */}
      <Transition
        show={!isLoading && isContentReady}
        enter="transition-all duration-500"
        enterFrom="opacity-0 scale-95"
        enterTo="opacity-100 scale-100"
        leave="transition-all duration-300"
        leaveFrom="opacity-100 scale-100"
        leaveTo="opacity-0 scale-95"
      >
        <div
          className={clsx(
            'flex flex-col gap-size2 max-h-[80vh] overflow-y-auto no-scrollbar',
            'animate-fadeIn'
          )}
        >
          {/* Billing period tabs - fixed position */}
          <div className="flex items-center justify-center  pb-4 pt-3 ">
            <div className="bg-white rounded-full shadow-sm p-1">
              {BILLING_PERIODS.map((billingPeriod) => (
                <button
                  key={billingPeriod.id}
                  onClick={() => setSelectedPeriod(billingPeriod.id)}
                  className={clsx(
                    'relative px-4 py-1.5 text-xs font-medium rounded-full transition-all duration-200',
                    selectedPeriod === billingPeriod.id
                      ? 'bg-purple-100 text-purple-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  )}
                >
                  {billingPeriod.label}
                  {billingPeriod.badge && selectedPeriod === billingPeriod.id && (
                    <span className="absolute top-1/2 -translate-y-1/2 -right-24 text-[10px] bg-green-50 text-green-700 px-1.5 py-0.5 rounded-full font-medium">
                      {billingPeriod.badge}
                    </span>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* Plans grid */}
          <div className={`
            grid gap-size2
            grid-cols-1
            sm:grid-cols-2

            ${showAddons && selectedPeriod === 'weekly'
              ? filteredTiers.length === 1
                ? 'lg:grid-cols-2 xl:grid-cols-2'
                : filteredTiers.length === 2
                ? 'lg:grid-cols-3 xl:grid-cols-3'
                : filteredTiers.length === 3
                ? 'lg:grid-cols-4 xl:grid-cols-4'
                : 'lg:grid-cols-5 xl:grid-cols-5'
              : filteredTiers.length === 1
              ? 'lg:grid-cols-1 xl:grid-cols-1 max-w-md mx-auto'
              : filteredTiers.length === 2
              ? 'lg:grid-cols-2 xl:grid-cols-2'
              : filteredTiers.length === 3
              ? 'lg:grid-cols-3 xl:grid-cols-3'
              : 'lg:grid-cols-4 xl:grid-cols-4'
            }
          `}>
            {/* Addons section - only shown for weekly plan and Starter/Advanced tiers */}
            {showAddons && selectedPeriod === 'weekly' && (
              <div className="flex flex-col gap-size3 animate-fadeInUpDelayed1">
                <div className="text-center mb-size1 pt-size4">
                  <h3 className="text-xl font-medium">Instant Add-ons</h3>
                  <p className="text-xs text-grey-50">Enhance your current plan with feature add-ons.</p>
                </div>

                {/* Addons as separate boxes */}
                {WEEKLY_ADDONS.map((addon) => (
                  <div
                    key={addon.id}
                    className="bg-white rounded-xl py-size3 px-size4 border border-grey-5 flex flex-col gap-size2 shadow-md transition-all duration-300 hover:scale-[1.01] overflow-hidden"
                  >
                    <div className="flex flex-col gap-0">
                      <div className="flex justify-between items-center">
                        <span className="text-xs font-medium">{addon.name}</span>
                        <span className="text-sm font-bold">${addon.price}/month</span>
                      </div>
                      <p className="text-xs text-grey-50 leading-none">{addon.description}</p>
                    </div>
                    <DButton
                      fullWidth
                      className="py-[15px] px-size2 text-white rounded-full mt-auto"
                      style={{
                        background: 'linear-gradient(87deg, rgb(100, 116, 255) -15.34%, rgb(128, 120, 253) 21.77%, rgb(207, 192, 254) 83.94%) 0% 0% / 123.26% 100% border-box'
                      }}
                      onClick={() => handleAddon(addon.id)}
                    >
                      {(user?.tier_type === 'advanced' || user?.tier_type === 'pro') && addon.id === 'remove_powered_by_dante_ai' ? 'This is already included in your plan' : 'Add'}
                    </DButton>
                  </div>
                ))}
              </div>
            )}

            {filteredTiers.map((tier, index) => {
              const isAdvanced = tier.name === 'Advanced';

              return (
                <div
                  key={tier.id}
                  className={`
                  bg-white rounded-xl
                  ${isAdvanced ? 'py-[27px] -mt-size2' : 'py-size4'}
                  ${selectedPeriod === 'weekly' ? 'px-size5' : 'px-size6'}
                  ${
                    isAdvanced
                      ? 'border-2 border-purple-200 shadow-2xl'
                      : 'border border-grey-5'
                  }
                  flex flex-col relative shadow-lg
                  ${
                    selectedPeriod === 'weekly'
                      ? index === 0
                        ? 'animate-fadeInUpDelayed2'
                        : 'animate-fadeInUpDelayed3'
                      : index === 0
                      ? 'animate-fadeInUpDelayed1'
                      : index === 1
                      ? 'animate-fadeInUpDelayed2'
                      : 'animate-fadeInUpDelayed3'
                  }
                  ${filteredTiers.length === 1 ? ' mx-auto' : ''}
                `}
                >
                  {/* Best Value Label for Advanced Plan */}
                  {isBestValue(tier.name) && (
                    <div className="absolute -top-2 right-[-3px] transform bg-gradient-to-r from-purple-600 to-purple-400 text-white px-4 py-2 rounded-md text-[10px] font-bold tracking-wider shadow-md">
                      BEST VALUE
                    </div>
                  )}

                  <div className="flex flex-col gap-[0.4rem]">
                    {/* Plan Header */}
                    <div className="text-center">
                      <h3 className="text-xl font-medium mb-0.5">
                        {tier.name}
                      </h3>
                      {/* <p className="text-xs text-grey-50">
                        {PLAN_DESCRIPTIONS[tier.name]}
                      </p> */}
                    </div>

                    {/* Price */}
                    <div className="flex gap-size1 items-center justify-center mb-size1">
                      <span className="text-2xl font-bold leading-none">
                        $
                        {period === 'yearly'
                          ? Math.round(tier.price / 12)
                          : tier.price}
                      </span>
                      <div className="flex flex-col gap-size0">
                        <p className="text-xs text-grey-50 leading-none">
                          per{' '}
                          {period === 'yearly'
                            ? 'month, billed annually'
                            : period === 'monthly'
                            ? 'month'
                            : 'week'}
                        </p>
                        {period !== 'weekly' && (
                          <p className="text-[10px] text-green-700 leading-none">
                            Save $
                            {getPlanSavings(
                              tier.price,
                              tiers?.['monthly']?.find(
                                (t) => t.name === tier.name
                              )?.price,
                              tiers?.['weekly']?.find(
                                (t) => t.name === tier.name
                              )?.price
                            )}{' '}
                            per {period === 'yearly' ? 'year' : period === 'monthly' ? 'month' : 'week'}
                          </p>
                        )}
                      </div>
                    </div>

                    {/* Button Logic */}
                    {user?.tier_rank === tier.rank && !user?.tier_on_trial && (
                      <DButton
                        variant="light"
                        fullWidth
                        className={`relative overflow-hidden border border-purple-200 ${
                          isAdvanced ? 'py-[20px] px-size2' : 'py-[20px] px-size2'
                        } text-black !rounded-full !mt-auto`}
                      >
                        Current Plan
                      </DButton>
                    )}
                    {user?.tier_rank > tier.rank &&
                      tier.name !== 'Enterprise' &&
                      !user?.tier_on_trial && (
                        <DButton
                          fullWidth
                          onClick={() => handleDowngrade(tier)}
                          className={`border border-purple-200 ${
                            isAdvanced ? 'py-[20px] px-size2' : 'py-[20px] px-size2'
                          } text-black rounded-full`}
                        >
                          Downgrade
                        </DButton>
                      )}
                    {(user?.tier_rank || 0) < tier.rank &&
                      tier.name !== 'Enterprise' &&
                      !user?.tier_on_trial && (
                        <DButton
                          fullWidth
                          onClick={() => handleUpgrade(tier.id)}
                          className={`border border-purple-200 ${
                            isAdvanced
                              ? 'py-[20px] px-size2 text-white'
                              : 'py-[20px] px-size2 text-black'
                          }  rounded-full`}
                          style={{
                            background: isAdvanced
                              ? 'linear-gradient(87deg, rgb(100, 116, 255) -15.34%, rgb(128, 120, 253) 21.77%, rgb(207, 192, 254) 83.94%) 0% 0% / 123.26% 100% border-box'
                              : undefined,
                          }}
                        >
                          Go {tier.name}
                        </DButton>
                      )}
                    {tier?.name !== 'Enterprise' && user?.tier_on_trial && (
                      <DButton
                        fullWidth
                        onClick={() => handleUpgrade(tier.id)}
                        className={`border border-purple-200 ${
                          isAdvanced
                            ? 'py-[20px] px-size2 text-white'
                            : 'py-[20px] px-size2 text-black'
                        }  rounded-full`}
                        style={{
                          background: isAdvanced
                            ? 'linear-gradient(87deg, rgb(100, 116, 255) -15.34%, rgb(128, 120, 253) 21.77%, rgb(207, 192, 254) 83.94%) 0% 0% / 123.26% 100% border-box'
                            : undefined,
                        }}
                      >
                        Go {tier.name}{' '}
                        {user?.tier_rank === tier.rank && user?.tier_on_trial}
                      </DButton>
                    )}

                    {/* Features Section */}
                    <div className="flex flex-col gap-size0 mx-auto mt-size2">
                      <ul className="flex flex-col gap-[0.15rem]">
                        {(tier.name === 'Free'
                          ? FREE_FEATURES
                          : tier.name === 'Starter'
                          ? STARTER_FEATURES
                          : tier.name === 'Advanced'
                          ? ADVANCED_FEATURES
                          : PRO_FEATURES
                        ).map((feature, featureIndex) => {
                          return (
                            <li
                              key={featureIndex}
                              className="flex items-center gap-size1"
                            >
                              <div className="w-3 flex items-center justify-center">
                                {feature.label.includes('Everything in') ? (
                                  <span className="text-xs text-grey-50"></span>
                                ) : (
                                  <CheckmarkIcon
                                    className={'text-purple-400 w-3 h-3'}
                                  />
                                )}
                              </div>
                              <div className="flex items-center gap-size1">
                                <span
                                  className={`text-xs leading-[1.7] ${
                                    feature.label.includes('Everything in')
                                      ? 'font-bold'
                                      : feature.underlineTooltip
                                      ? 'border-b border-dotted border-gray-400'
                                      : ''
                                  }`}
                                >
                                  {!feature.underlineTooltip ? (
                                    <span className="flex items-center gap-1">
                                      {feature.label}
                                      {feature.showNewBadge && <NewBadge className="ml-1" />}
                                    </span>
                                  ) : (
                                    <DTooltip content={feature.content} position="bottom">
                                      <span className="flex items-center gap-1">
                                        {feature.label}
                                        {feature.showNewBadge && <NewBadge className="ml-1" />}
                                      </span>
                                    </DTooltip>
                                  )}
                                </span>
                                {feature.hasTooltip && (
                                  <DTooltip
                                    position="bottom"
                                    content={`
                                      <div className="max-w-[250px]">
                                        <p>
                                          ${MESSAGES_TOOLTIP}
                                          <a
                                            href="https://www.dante-ai.com/pricing"
                                            target="_blank"
                                            rel="noopener noreferrer"
                                            class="!text-white underline"
                                          >
                                            Learn more
                                          </a>
                                        </p>
                                      </div>
                                    `}
                                  >
                                    <InfoIcon className="w-3 h-3 text-grey-50 cursor-help" />
                                  </DTooltip>
                                )}
                              </div>
                            </li>
                          );
                        })}
                      </ul>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className="flex justify-center">
            <a
              href="https://www.dante-ai.com/pricing"
              className="text-purple-300 text-xs flex items-center gap-size1"
              target="_blank"
              // onClick={(e) => {
              //   e.preventDefault();
              //   onClose();
              //   window.location.href = '/plans';
              // }}
            >
              See all features <span className="text-sm">→</span>
            </a>
          </div>

          {/* Enterprise section */}
          <div className="rounded-xl p-3  border border-grey-5 flex items-center justify-between animate-fadeInUpDelayed3 max-w-[60%] w-full mx-auto">
            <div className="flex flex-col gap-size0">
              <h3 className="text-base font-medium">Enterprise</h3>
              <p className="text-xs text-grey-50">
                Unlimited access with Dante AI engineering support for bespoke solutions.
              </p>
            </div>
            <div className="flex items-center gap-3">
              <DButton
                className="border border-purple-200 p-size2 px-size3 text-black rounded-full"
                size="xs"
                onClick={handleContactSales}
              >
                Contact Us
              </DButton>
            </div>
          </div>
        </div>
      </Transition>
      <DModalDowngrade
        isOpen={isDowngradeModalOpen}
        onClose={() => setIsDowngradeModalOpen(false)}
        nextTier={nextTier}
      />
    </DModal>
  );
};

export default DModalPlans;
