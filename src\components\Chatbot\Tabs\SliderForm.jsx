import { useState } from 'react';
import DDraggableContainer from '@/components/Global/DDraggableContainer';
import AddIcon from '@/components/Global/Icons/AddIcon';
import DButton from '@/components/Global/DButton';
import DCheckbox from '@/components/Global/DCheckbox';
import DInput from '@/components/Global/DInput/DInput';
import SliderInput from '@/components/Global/SliderInput';
import { useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';

const SliderForm = ({ closeModal, item, slots, setSlots, chatbotId }) => {
    const params = useParams();
    const [sliderTitle, setSliderTitle] = useState(item?.title ?? '');
    const [sliderItems, setSliderItems] = useState(item?.items?.map(item => ({
        ...item,
        frontend_id: item.id,
    })) ?? [{
        frontend_id: uuidv4(),
        url: 'https://',
        name: '',
        description: '',
        thumbnail_url: 'https://',
        title: '',
    }]);
    const [openAllInNewTab, setOpenAllInNewTab] = useState(item?.open_all_in_new_tab ?? true);
    const [error, setError] = useState({
        title: null,
    });
    const [submitted, setSubmitted] = useState(false);


    const validateForm = () => {
        let valid = true;
    
        if (sliderTitle.trim() === '') {
            setError((prev) => ({ ...prev, title: 'Slider title is required' }));
            valid = false;
        } else {
            setError((prev) => ({ ...prev, title: null }));
        }
    
        if (sliderItems.length === 0) {
            setError((prev) => ({ ...prev, items: 'At least one item is required' }));
            valid = false;
        } else {
            const invalidItems = sliderItems.some((item) => !item.url?.trim());
            if (invalidItems) {
                setError((prev) => ({ ...prev, items: 'All slider items must have valid URLs.' }));
                valid = false;
            } else {
                setError((prev) => ({ ...prev, items: null }));
            }
        }
    
        return valid;
    };
    

    const handleSubmit = () => {
        setSubmitted(true);
        if (!validateForm()) return;
        try{
            const payload = {
                kb_id: params.id ?? chatbotId,
                title: sliderTitle,
                frontend_id: uuidv4(),
                items: sliderItems.map((item, index) => ({
                    ...item,
                    order: index
                })),
                open_all_in_new_tab: openAllInNewTab,
                order: slots.length + 1
            }
            setSlots((prevSlots) => [...prevSlots, { ...payload, type: 'slider', disclaimer: 'Slider' }]);
            closeModal();
        }catch(error){
            console.error('Error creating slider:', error);
        }
    }

    const handleUpdate = () => {
        setSubmitted(true);
        if (!validateForm()) return;
        try{
            setSlots((prevSlots) => 
                prevSlots.map(slot => 
                    (slot.frontend_id && item.frontend_id ? slot.frontend_id === item.frontend_id : slot.id === item.id) ? { 
                        ...slot, 
                        title: sliderTitle,
                        frontend_id: item.frontend_id,
                        order: item.order,
                        items: sliderItems.map((item, index) => ({
                            ...item,
                            order: index
                        })),
                    } : slot
                )
            );
            closeModal();
        }catch(error){
            console.error('Error updating slider:', error);
        }
    }

    const handleEditLink = (id, newData) => {
        setSliderItems(sliderItems.map(item => item.id === id ? newData : item));
    }

    const handleDeleteLink = (id) => {
        setSliderItems(sliderItems.filter(item => item.id !== id));
    }

    const handleAddLink = () => {
        setSliderItems((prev) => [
            ...prev,
            { id: uuidv4(), url: 'https://', name: '', description: '', thumbnail_url: 'https://' },
        ]);
    };

  return (
    <div className="flex flex-col gap-size5">
        <div className="flex flex-col gap-size1">
            <p className="text-base font-medium tracking-tight">Slider title</p>
            <DInput
              placeholder="Enter link title"
              value={sliderTitle}
              onChange={(e) => setSliderTitle(e.target.value)}
              error={submitted ? error.title : ''}
            />
        <div className="w-full h-[1px] bg-grey-5"></div>
        </div>
        <div className="flex flex-col gap-size1 overflow-y-auto max-h-[330px] no-scrollbar">
            <DDraggableContainer
                items={sliderItems}
                setItems={setSliderItems}
                ItemComponent={SliderInput}
                onEdit={handleEditLink}
                onDelete={handleDeleteLink}
                error={error}
            />
        </div>
        <button className="dbutton flex gap-size1 items-center text-xs tracking-tight" onClick={handleAddLink}>
            <AddIcon />
            <span>Add another URL</span>
        </button>
        <div className="w-full h-[1px] bg-grey-5"></div>
        <DCheckbox
        label="Open all in new tab"
        checked={openAllInNewTab}
            onChange={(checked) => setOpenAllInNewTab(checked)}
        />
        {item && Object.keys(item).length > 0 ? (
            <DButton variant="dark" onClick={handleUpdate} fullWidth>Update</DButton>
        ) : (
            <DButton variant="dark" onClick={handleSubmit} fullWidth>Complete</DButton>
        )}
    </div>
  )
}

export default SliderForm;